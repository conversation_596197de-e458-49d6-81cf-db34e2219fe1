<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cicilan', function (Blueprint $table) {
            $table->id('id_cicilan');
            $table->foreignId('id_tagihan')->constrained('tagihan', 'id_tagihan');
            $table->integer('cicilan_ke');
            $table->decimal('jumlah_cicilan', 12, 2);
            $table->date('tanggal_jatuh_tempo');
            $table->date('tanggal_dibayar')->nullable();
            $table->decimal('jumlah_dibayar', 12, 2)->default(0);
            $table->enum('status_cicilan', ['belum_dibayar', 'dibayar', 'terlambat'])->default('belum_dibayar');
            $table->text('catatan')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['id_tagihan', 'cicilan_ke']);
            $table->index(['status_cicilan', 'tanggal_jatuh_tempo']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cicilan');
    }
};
