<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Peran;
use App\Models\HakAkses;

class HakAksesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Modules dalam sistem klinik gigi
        $modules = [
            'dashboard',
            'pasien',
            'janji_temu',
            'rekam_medis',
            'charting_gigi',
            'tagihan',
            'pembayaran',
            'inventori',
            'komunikasi',
            'laporan',
            'pengaturan',
            'pengguna',
        ];

        // Permissions untuk setiap role
        $rolePermissions = [
            'Super Admin' => [
                // Super Admin memiliki akses penuh ke semua modul
                'dashboard' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'pasien' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'janji_temu' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'rekam_medis' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'charting_gigi' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'tagihan' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'pembayaran' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'inventori' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'komunikasi' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'laporan' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'pengaturan' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'pengguna' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
            ],
            'Admin Klinik' => [
                'dashboard' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'pasien' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'janji_temu' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => true],
                'rekam_medis' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'tagihan' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'pembayaran' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'inventori' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'komunikasi' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'laporan' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'pengaturan' => ['baca' => true, 'tulis' => false, 'ubah' => true, 'hapus' => false],
                'pengguna' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
            ],
            'Dokter Gigi' => [
                'dashboard' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'pasien' => ['baca' => true, 'tulis' => false, 'ubah' => true, 'hapus' => false],
                'janji_temu' => ['baca' => true, 'tulis' => false, 'ubah' => true, 'hapus' => false],
                'rekam_medis' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'charting_gigi' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'tagihan' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'inventori' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'komunikasi' => ['baca' => true, 'tulis' => true, 'ubah' => false, 'hapus' => false],
            ],
            'Resepsionis' => [
                'dashboard' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'pasien' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'janji_temu' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'tagihan' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'komunikasi' => ['baca' => true, 'tulis' => true, 'ubah' => false, 'hapus' => false],
            ],
            'Kasir' => [
                'dashboard' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'pasien' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
                'tagihan' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'pembayaran' => ['baca' => true, 'tulis' => true, 'ubah' => true, 'hapus' => false],
                'laporan' => ['baca' => true, 'tulis' => false, 'ubah' => false, 'hapus' => false],
            ],
        ];

        foreach ($rolePermissions as $roleName => $permissions) {
            $role = Peran::where('nama_peran', $roleName)->first();
            
            if ($role) {
                foreach ($permissions as $module => $actions) {
                    HakAkses::create([
                        'id_peran' => $role->id_peran,
                        'modul' => $module,
                        'baca' => $actions['baca'] ?? false,
                        'tulis' => $actions['tulis'] ?? false,
                        'ubah' => $actions['ubah'] ?? false,
                        'hapus' => $actions['hapus'] ?? false,
                    ]);
                }
            }
        }
    }
}
