[![Nette Utils](https://github.com/nette/utils/assets/194960/c33fdb74-0652-4cad-ac6e-c1ce0d29e32a)](https://doc.nette.org/en/utils)

[![Downloads this Month](https://img.shields.io/packagist/dm/nette/utils.svg)](https://packagist.org/packages/nette/utils)
[![Tests](https://github.com/nette/utils/workflows/Tests/badge.svg?branch=master)](https://github.com/nette/utils/actions)
[![Coverage Status](https://coveralls.io/repos/github/nette/utils/badge.svg?branch=master)](https://coveralls.io/github/nette/utils?branch=master)
[![Latest Stable Version](https://poser.pugx.org/nette/utils/v/stable)](https://github.com/nette/utils/releases)
[![License](https://img.shields.io/badge/license-New%20BSD-blue.svg)](https://github.com/nette/utils/blob/master/license.md)


Introduction
------------

In package nette/utils you will find a set of useful classes for everyday use:

✅ [Arrays](https://doc.nette.org/utils/arrays)<br>
✅ [Callback](https://doc.nette.org/utils/callback) - PHP callbacks<br>
✅ [Filesystem](https://doc.nette.org/utils/filesystem) - copying, renaming, …<br>
✅ [Finder](https://doc.nette.org/utils/finder) - finds files and directories<br>
✅ [Floats](https://doc.nette.org/utils/floats) - floating point numbers<br>
✅ [Helper Functions](https://doc.nette.org/utils/helpers)<br>
✅ [HTML elements](https://doc.nette.org/utils/html-elements) - generate HTML<br>
✅ [Images](https://doc.nette.org/utils/images) - crop, resize, rotate images<br>
✅ [Iterables](https://doc.nette.org/utils/iterables) <br>
✅ [JSON](https://doc.nette.org/utils/json) - encoding and decoding<br>
✅ [Generating Random Strings](https://doc.nette.org/utils/random)<br>
✅ [Paginator](https://doc.nette.org/utils/paginator) - pagination math<br>
✅ [PHP Reflection](https://doc.nette.org/utils/reflection)<br>
✅ [Strings](https://doc.nette.org/utils/strings) - useful text functions<br>
✅ [SmartObject](https://doc.nette.org/utils/smartobject) - PHP object enhancements<br>
✅ [Type](https://doc.nette.org/utils/type) - PHP data type<br>
✅ [Validation](https://doc.nette.org/utils/validators) - validate inputs<br>

 <!---->

Installation
------------

The recommended way to install is via Composer:

```
composer require nette/utils
```

Nette Utils 4.0 is compatible with PHP 8.0 to 8.4.

 <!---->

[Support Me](https://github.com/sponsors/dg)
--------------------------------------------

Do you like Nette Utils? Are you looking forward to the new features?

[![Buy me a coffee](https://files.nette.org/icons/donation-3.svg)](https://github.com/sponsors/dg)

Thank you!
