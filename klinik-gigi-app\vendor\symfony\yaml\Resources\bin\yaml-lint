#!/usr/bin/env php
<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

if ('cli' !== \PHP_SAPI) {
    throw new Exception('This script must be run from the command line.');
}

/**
 * Runs the Yaml lint command.
 *
 * <AUTHOR> <jan.scha<PERSON><PERSON>@sensiolabs.de>
 */

use Symfony\Component\Console\Application;
use Symfony\Component\Yaml\Command\LintCommand;

function includeIfExists(string $file): bool
{
    return file_exists($file) && include $file;
}

if (
    !includeIfExists(__DIR__ . '/../../../../autoload.php') &&
    !includeIfExists(__DIR__ . '/../../vendor/autoload.php') &&
    !includeIfExists(__DIR__ . '/../../../../../../vendor/autoload.php')
) {
    fwrite(STDERR, 'Install dependencies using Composer.'.PHP_EOL);
    exit(1);
}

if (!class_exists(Application::class)) {
    fwrite(STDERR, 'You need the "symfony/console" component in order to run the Yaml linter.'.PHP_EOL);
    exit(1);
}

(new Application())->add($command = new LintCommand())
    ->getApplication()
    ->setDefaultCommand($command->getName(), true)
    ->run()
;
