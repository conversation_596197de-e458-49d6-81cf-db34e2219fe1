<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Pengguna;
use App\Models\Peran;
use Illuminate\Support\Facades\Hash;

class PenggunaSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $superAdminRole = Peran::where('nama_peran', 'Super Admin')->first();
        $adminRole = Peran::where('nama_peran', 'Admin Klinik')->first();
        $dokterRole = Peran::where('nama_peran', 'Dokter Gigi')->first();
        $resepsionisRole = Peran::where('nama_peran', 'Resepsionis')->first();

        $users = [
            [
                'username' => 'superadmin',
                'email' => '<EMAIL>',
                'password_hash' => Hash::make('password123'),
                'nama_depan' => 'Super',
                'nama_belakang' => 'Administrator',
                'no_telepon' => '081234567890',
                'id_peran' => $superAdminRole?->id_peran,
                'aktif' => true,
            ],
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password_hash' => Hash::make('password123'),
                'nama_depan' => 'Admin',
                'nama_belakang' => 'Klinik',
                'no_telepon' => '081234567891',
                'id_peran' => $adminRole?->id_peran,
                'aktif' => true,
            ],
            [
                'username' => 'drg.john',
                'email' => '<EMAIL>',
                'password_hash' => Hash::make('password123'),
                'nama_depan' => 'Dr. John',
                'nama_belakang' => 'Smith',
                'no_telepon' => '081234567892',
                'id_peran' => $dokterRole?->id_peran,
                'nomor_str' => 'STR123456789',
                'nomor_sip' => 'SIP987654321',
                'expired_str' => '2025-12-31',
                'expired_sip' => '2025-12-31',
                'aktif' => true,
            ],
            [
                'username' => 'drg.sarah',
                'email' => '<EMAIL>',
                'password_hash' => Hash::make('password123'),
                'nama_depan' => 'Dr. Sarah',
                'nama_belakang' => 'Johnson',
                'no_telepon' => '081234567893',
                'id_peran' => $dokterRole?->id_peran,
                'nomor_str' => 'STR123456790',
                'nomor_sip' => 'SIP987654322',
                'expired_str' => '2025-12-31',
                'expired_sip' => '2025-12-31',
                'aktif' => true,
            ],
            [
                'username' => 'resepsionis',
                'email' => '<EMAIL>',
                'password_hash' => Hash::make('password123'),
                'nama_depan' => 'Maria',
                'nama_belakang' => 'Resepsionis',
                'no_telepon' => '081234567894',
                'id_peran' => $resepsionisRole?->id_peran,
                'aktif' => true,
            ],
        ];

        foreach ($users as $user) {
            Pengguna::create($user);
        }
    }
}
