<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Peran;

class PeranSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'nama_peran' => 'Super Admin',
                'deskripsi' => 'Administrator sistem dengan akses penuh',
                'hak_akses' => [
                    'all_modules' => true,
                    'system_settings' => true,
                    'user_management' => true,
                ]
            ],
            [
                'nama_peran' => 'Admin Klinik',
                'deskripsi' => 'Administrator klinik dengan akses manajemen',
                'hak_akses' => [
                    'patient_management' => true,
                    'appointment_management' => true,
                    'billing_management' => true,
                    'inventory_management' => true,
                    'reports' => true,
                ]
            ],
            [
                'nama_peran' => 'Dokter Gigi',
                'deskripsi' => 'Dokter gigi dengan akses medical records',
                'hak_akses' => [
                    'patient_records' => true,
                    'medical_records' => true,
                    'appointments' => true,
                    'prescriptions' => true,
                    'dental_charting' => true,
                ]
            ],
            [
                'nama_peran' => 'Perawat',
                'deskripsi' => 'Perawat dengan akses terbatas',
                'hak_akses' => [
                    'patient_basic_info' => true,
                    'appointments_view' => true,
                    'inventory_usage' => true,
                ]
            ],
            [
                'nama_peran' => 'Resepsionis',
                'deskripsi' => 'Resepsionis dengan akses front office',
                'hak_akses' => [
                    'patient_registration' => true,
                    'appointment_scheduling' => true,
                    'billing_basic' => true,
                    'communication' => true,
                ]
            ],
            [
                'nama_peran' => 'Kasir',
                'deskripsi' => 'Kasir dengan akses billing dan pembayaran',
                'hak_akses' => [
                    'billing_management' => true,
                    'payment_processing' => true,
                    'financial_reports' => true,
                ]
            ],
        ];

        foreach ($roles as $role) {
            Peran::create($role);
        }
    }
}
