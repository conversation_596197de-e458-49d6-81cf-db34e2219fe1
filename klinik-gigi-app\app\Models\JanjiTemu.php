<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class JanjiTemu extends Model
{
    use HasFactory;

    protected $table = 'janji_temu';
    protected $primaryKey = 'id_janji_temu';

    protected $fillable = [
        'id_pasien',
        'id_dokter',
        'id_jenis_perawatan',
        'id_ruang',
        'tanggal_janji',
        'jam_mulai',
        'jam_selesai',
        'status',
        'estimasi_biaya',
        'catatan',
        'alasan_batal',
    ];

    protected $casts = [
        'tanggal_janji' => 'datetime',
        'jam_mulai' => 'datetime:H:i',
        'jam_selesai' => 'datetime:H:i',
        'estimasi_biaya' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: JanjiTemu belongs to Pasien
     */
    public function pasien(): BelongsTo
    {
        return $this->belongsTo(Pasien::class, 'id_pasien', 'id_pasien');
    }

    /**
     * Relationship: JanjiTemu belongs to Pengguna (dokter)
     */
    public function dokter(): BelongsTo
    {
        return $this->belongsTo(Pengguna::class, 'id_dokter', 'id_pengguna');
    }

    /**
     * Relationship: JanjiTemu belongs to JenisPerawatan
     */
    public function jenisPerawatan(): BelongsTo
    {
        return $this->belongsTo(JenisPerawatan::class, 'id_jenis_perawatan', 'id_jenis_perawatan');
    }

    /**
     * Relationship: JanjiTemu belongs to RuangPerawatan
     */
    public function ruangPerawatan(): BelongsTo
    {
        return $this->belongsTo(RuangPerawatan::class, 'id_ruang', 'id_ruang');
    }

    /**
     * Relationship: JanjiTemu has many PerawatanJanji
     */
    public function perawatanJanji(): HasMany
    {
        return $this->hasMany(PerawatanJanji::class, 'id_janji_temu', 'id_janji_temu');
    }

    /**
     * Relationship: JanjiTemu has one RekamMedis
     */
    public function rekamMedis(): HasOne
    {
        return $this->hasOne(RekamMedis::class, 'id_janji_temu', 'id_janji_temu');
    }

    /**
     * Relationship: JanjiTemu has one Tagihan
     */
    public function tagihan(): HasOne
    {
        return $this->hasOne(Tagihan::class, 'id_janji_temu', 'id_janji_temu');
    }

    /**
     * Scope: Filter by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Today's appointments
     */
    public function scopeToday($query)
    {
        return $query->whereDate('tanggal_janji', today());
    }

    /**
     * Scope: Upcoming appointments
     */
    public function scopeUpcoming($query)
    {
        return $query->where('tanggal_janji', '>=', now())
                    ->where('status', 'dijadwalkan');
    }

    /**
     * Scope: By doctor
     */
    public function scopeByDokter($query, $dokterId)
    {
        return $query->where('id_dokter', $dokterId);
    }

    /**
     * Check if appointment can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['dijadwalkan']) &&
               $this->tanggal_janji->isFuture();
    }

    /**
     * Check if appointment is today
     */
    public function isToday(): bool
    {
        return $this->tanggal_janji->isToday();
    }

    /**
     * Get duration in minutes
     */
    public function getDurasiMenitAttribute(): int
    {
        if ($this->jam_mulai && $this->jam_selesai) {
            return $this->jam_selesai->diffInMinutes($this->jam_mulai);
        }
        return $this->jenisPerawatan?->durasi_estimasi_menit ?? 0;
    }
}
