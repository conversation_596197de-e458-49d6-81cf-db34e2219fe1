<?php

namespace App\Http\Controllers;

use App\Models\Pasien;
use Illuminate\Http\Request;
use Inertia\Inertia;

class PasienController extends Controller
{
    /**
     * Constructor - Apply RBAC middleware
     */
    public function __construct()
    {
        $this->middleware('permission:pasien,baca')->only(['index', 'show']);
        $this->middleware('permission:pasien,tulis')->only(['create', 'store']);
        $this->middleware('permission:pasien,ubah')->only(['edit', 'update']);
        $this->middleware('permission:pasien,hapus')->only(['destroy']);
    }

    /**
     * Display a listing of patients.
     */
    public function index(Request $request)
    {
        $query = Pasien::query()->with('keluarga');

        // Search functionality
        if ($request->has('search')) {
            $query->search($request->search);
        }

        // Filter by status
        if ($request->has('status')) {
            if ($request->status === 'active') {
                $query->active();
            }
        }

        $pasien = $query->paginate(15);

        return Inertia::render('Pasien/Index', [
            'pasien' => $pasien,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Show the form for creating a new patient.
     */
    public function create()
    {
        return Inertia::render('Pasien/Create');
    }

    /**
     * Store a newly created patient.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nama_depan' => 'required|string|max:255',
            'nama_belakang' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'no_telepon' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'alamat' => 'nullable|string',
            'kota' => 'nullable|string|max:100',
            'provinsi' => 'nullable|string|max:100',
            'nik' => 'nullable|string|max:16|unique:pasien,nik',
            'nomor_bpjs' => 'nullable|string|max:13',
            'kelas_bpjs' => 'nullable|in:I,II,III',
        ]);

        // Generate nomor pasien
        $lastPasien = Pasien::latest('id_pasien')->first();
        $nextNumber = $lastPasien ? (int)substr($lastPasien->nomor_pasien, -4) + 1 : 1;
        $validated['nomor_pasien'] = 'P' . date('Y') . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);

        $pasien = Pasien::create($validated);

        return redirect()->route('pasien.index')
            ->with('success', 'Pasien berhasil ditambahkan.');
    }

    /**
     * Display the specified patient.
     */
    public function show(Pasien $pasien)
    {
        $pasien->load([
            'janjiTemu.dokter',
            'rekamMedis.dokter',
            'tagihan',
            'komunikasi'
        ]);

        return Inertia::render('Pasien/Show', [
            'pasien' => $pasien,
        ]);
    }

    /**
     * Show the form for editing the specified patient.
     */
    public function edit(Pasien $pasien)
    {
        return Inertia::render('Pasien/Edit', [
            'pasien' => $pasien,
        ]);
    }

    /**
     * Update the specified patient.
     */
    public function update(Request $request, Pasien $pasien)
    {
        $validated = $request->validate([
            'nama_depan' => 'required|string|max:255',
            'nama_belakang' => 'required|string|max:255',
            'tanggal_lahir' => 'required|date',
            'jenis_kelamin' => 'required|in:L,P',
            'no_telepon' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'alamat' => 'nullable|string',
            'kota' => 'nullable|string|max:100',
            'provinsi' => 'nullable|string|max:100',
            'nik' => 'nullable|string|max:16|unique:pasien,nik,' . $pasien->id_pasien . ',id_pasien',
            'nomor_bpjs' => 'nullable|string|max:13',
            'kelas_bpjs' => 'nullable|in:I,II,III',
            'aktif' => 'boolean',
        ]);

        $pasien->update($validated);

        return redirect()->route('pasien.index')
            ->with('success', 'Data pasien berhasil diperbarui.');
    }

    /**
     * Remove the specified patient (soft delete).
     */
    public function destroy(Pasien $pasien)
    {
        $pasien->update(['aktif' => false]);

        return redirect()->route('pasien.index')
            ->with('success', 'Pasien berhasil dinonaktifkan.');
    }
}
