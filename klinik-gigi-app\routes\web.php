<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use App\Http\Controllers\PasienController;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Klinik Gigi Routes with RBAC
    Route::resource('pasien', PasienController::class);

    // Additional routes can be added here
    // Route::resource('janji-temu', JanjiTemuController::class);
    // Route::resource('rekam-medis', RekamMedisController::class);
    // Route::resource('tagihan', TagihanController::class);
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
