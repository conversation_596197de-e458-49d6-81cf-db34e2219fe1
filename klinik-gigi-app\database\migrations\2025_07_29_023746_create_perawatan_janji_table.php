<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('perawatan_janji', function (Blueprint $table) {
            $table->id('id_perawatan_janji');
            $table->foreignId('id_janji_temu')->constrained('janji_temu', 'id_janji_temu')->onDelete('cascade');
            $table->foreignId('id_jenis_perawatan')->constrained('jenis_perawatan', 'id_jenis_perawatan');
            $table->integer('kuantitas')->default(1);
            $table->decimal('harga_satuan', 12, 2);
            $table->decimal('total_harga', 12, 2);
            $table->text('catatan')->nullable();
            $table->timestamps();
            
            // Indexes
            $table->index(['id_janji_temu', 'id_jenis_perawatan']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('perawatan_janji');
    }
};
