<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckRole
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$roles): Response
    {
        $user = $request->user();

        // Check if user is authenticated
        if (!$user) {
            return redirect()->route('login');
        }

        // Check if user has any of the required roles
        $userRole = $user->peran?->nama_peran;

        if (!$userRole || !in_array($userRole, $roles)) {
            abort(403, 'Anda tidak memiliki role yang diperlukan untuk mengakses halaman ini.');
        }

        return $next($request);
    }
}
